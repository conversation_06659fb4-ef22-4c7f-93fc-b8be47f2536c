import java.net.*;
import java.util.*;
import java.util.concurrent.*;

/**
 * 网络扫描工具 - 扫描指定网段中的在线设备
 */
public class NetworkScanner {
    
    public static void main(String[] args) {
        String baseIp = "172.16.60";  // 网段前缀
        int startRange = 1;           // 扫描起始IP
        int endRange = 254;           // 扫描结束IP
        
        System.out.println("=== 网络设备扫描工具 ===");
        System.out.println("扫描网段: " + baseIp + ".1-254");
        System.out.println("正在扫描，请稍候...\n");
        
        // 扫描在线设备
        List<String> onlineDevices = scanNetwork(baseIp, startRange, endRange);
        
        // 显示结果
        System.out.println("扫描完成！");
        System.out.println("发现 " + onlineDevices.size() + " 个在线设备：");
        System.out.println("==========================================");
        
        if (onlineDevices.isEmpty()) {
            System.out.println("未发现任何在线设备");
        } else {
            for (String ip : onlineDevices) {
                System.out.println("✓ " + ip + " - 在线");
                // 对每个在线设备进行端口扫描
                scanDevicePorts(ip);
            }
        }
        
        // 特别检查目标LED设备
        String targetLed = "*************";
        System.out.println("\n特别检查目标LED设备: " + targetLed);
        System.out.println("==========================================");
        checkSpecificDevice(targetLed);
    }
    
    /**
     * 扫描网段中的在线设备
     */
    public static List<String> scanNetwork(String baseIp, int start, int end) {
        List<String> onlineDevices = new ArrayList<>();
        ExecutorService executor = Executors.newFixedThreadPool(50); // 使用线程池加速扫描
        List<Future<String>> futures = new ArrayList<>();
        
        // 提交扫描任务
        for (int i = start; i <= end; i++) {
            final String ip = baseIp + "." + i;
            Future<String> future = executor.submit(() -> {
                try {
                    InetAddress address = InetAddress.getByName(ip);
                    if (address.isReachable(1000)) { // 1秒超时
                        return ip;
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
                return null;
            });
            futures.add(future);
        }
        
        // 收集结果
        for (Future<String> future : futures) {
            try {
                String result = future.get(2, TimeUnit.SECONDS);
                if (result != null) {
                    onlineDevices.add(result);
                }
            } catch (Exception e) {
                // 忽略超时等异常
            }
        }
        
        executor.shutdown();
        Collections.sort(onlineDevices, (a, b) -> {
            int ipA = Integer.parseInt(a.substring(a.lastIndexOf('.') + 1));
            int ipB = Integer.parseInt(b.substring(b.lastIndexOf('.') + 1));
            return Integer.compare(ipA, ipB);
        });
        
        return onlineDevices;
    }
    
    /**
     * 扫描设备的常用端口
     */
    public static void scanDevicePorts(String ip) {
        int[] commonPorts = {22, 23, 80, 443, 5005, 5006, 8080, 8888};
        List<Integer> openPorts = new ArrayList<>();
        
        for (int port : commonPorts) {
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(ip, port), 500);
                openPorts.add(port);
            } catch (Exception e) {
                // 端口关闭
            }
        }
        
        if (!openPorts.isEmpty()) {
            System.out.print("    开放端口: ");
            for (int i = 0; i < openPorts.size(); i++) {
                System.out.print(openPorts.get(i));
                if (i < openPorts.size() - 1) System.out.print(", ");
            }
            System.out.println();
        }
    }
    
    /**
     * 检查特定设备的详细信息
     */
    public static void checkSpecificDevice(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            
            // 基础连通性测试
            System.out.print("网络可达性测试: ");
            boolean reachable = address.isReachable(3000);
            System.out.println(reachable ? "✓ 成功" : "✗ 失败");
            
            if (!reachable) {
                System.out.println("设备离线或无响应");
                System.out.println("\n可能的原因:");
                System.out.println("1. LED设备未开机");
                System.out.println("2. 网线未连接或损坏");
                System.out.println("3. LED控制卡故障");
                System.out.println("4. IP地址配置错误");
                System.out.println("5. 防火墙阻止了ICMP包");
                return;
            }
            
            // 端口扫描
            System.out.println("\nLED控制端口扫描:");
            int[] ledPorts = {5005, 5006, 5007, 5008, 8080, 8888, 9999, 10000, 10001};
            boolean foundLedPort = false;
            
            for (int port : ledPorts) {
                try (Socket socket = new Socket()) {
                    socket.connect(new InetSocketAddress(ip, port), 1000);
                    System.out.println("✓ 端口 " + port + " 开放 - 可能是LED控制端口");
                    foundLedPort = true;
                } catch (Exception e) {
                    System.out.println("- 端口 " + port + " 关闭");
                }
            }
            
            if (foundLedPort) {
                System.out.println("\n✓ 发现LED控制服务，设备可能正常工作");
            } else {
                System.out.println("\n✗ 未发现LED控制端口，可能需要:");
                System.out.println("1. 检查LED控制卡配置");
                System.out.println("2. 确认控制软件是否启动");
                System.out.println("3. 查看设备说明书确认正确端口");
            }
            
        } catch (Exception e) {
            System.out.println("检查设备时出错: " + e.getMessage());
        }
    }
}
