import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

/**
 * 内存监控工具 - 用于检测内存泄漏
 */
public class MemoryMonitor {
    
    private static final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private long initialHeapUsed;
    private long initialNonHeapUsed;
    
    public MemoryMonitor() {
        recordInitialMemory();
    }
    
    /**
     * 记录初始内存使用情况
     */
    public void recordInitialMemory() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        this.initialHeapUsed = heapUsage.getUsed();
        this.initialNonHeapUsed = nonHeapUsage.getUsed();
        
        System.out.println("=== 初始内存状态 ===");
        printMemoryUsage("堆内存", heapUsage);
        printMemoryUsage("非堆内存", nonHeapUsage);
    }
    
    /**
     * 打印当前内存使用情况
     */
    public void printCurrentMemory(String label) {
        System.out.println("\n=== " + label + " ===");
        
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        printMemoryUsage("堆内存", heapUsage);
        printMemoryUsage("非堆内存", nonHeapUsage);
        
        // 计算内存变化
        long heapDelta = heapUsage.getUsed() - initialHeapUsed;
        long nonHeapDelta = nonHeapUsage.getUsed() - initialNonHeapUsed;
        
        System.out.println("内存变化:");
        System.out.println("  堆内存变化: " + formatBytes(heapDelta) + 
                          (heapDelta > 0 ? " ⬆" : heapDelta < 0 ? " ⬇" : " ➡"));
        System.out.println("  非堆内存变化: " + formatBytes(nonHeapDelta) + 
                          (nonHeapDelta > 0 ? " ⬆" : nonHeapDelta < 0 ? " ⬇" : " ➡"));
    }
    
    /**
     * 强制垃圾回收并检查内存
     */
    public void forceGCAndCheck(String label) {
        System.out.println("\n🗑 执行垃圾回收...");
        
        // 多次调用GC确保充分回收
        for (int i = 0; i < 3; i++) {
            System.gc();
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        printCurrentMemory(label + "（GC后）");
    }
    
    /**
     * 打印内存使用详情
     */
    private void printMemoryUsage(String type, MemoryUsage usage) {
        System.out.println(type + ":");
        System.out.println("  已使用: " + formatBytes(usage.getUsed()));
        System.out.println("  已提交: " + formatBytes(usage.getCommitted()));
        System.out.println("  最大值: " + formatBytes(usage.getMax()));
        System.out.println("  使用率: " + String.format("%.1f%%", 
                          (double)usage.getUsed() / usage.getCommitted() * 100));
    }
    
    /**
     * 格式化字节数为可读格式
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) {
            return "-" + formatBytes(-bytes);
        }
        
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 内存泄漏测试
     */
    public static void testMemoryLeak() {
        MemoryMonitor monitor = new MemoryMonitor();
        
        System.out.println("\n🧪 开始内存泄漏测试...");
        
        // 测试原始版本（模拟内存泄漏）
        System.out.println("\n--- 模拟原始版本的内存泄漏 ---");
        for (int i = 0; i < 10; i++) {
            // 模拟CreateProgram但不DeleteProgram的情况
            simulateMemoryLeak();
        }
        monitor.printCurrentMemory("模拟泄漏后");
        monitor.forceGCAndCheck("模拟泄漏GC后");
        
        // 测试安全版本
        System.out.println("\n--- 测试安全版本 ---");
        for (int i = 0; i < 10; i++) {
            testSafeVersion();
        }
        monitor.printCurrentMemory("安全版本测试后");
        monitor.forceGCAndCheck("安全版本GC后");
        
        System.out.println("\n✅ 内存测试完成");
    }
    
    /**
     * 模拟内存泄漏（创建对象但不释放）
     */
    private static void simulateMemoryLeak() {
        // 模拟创建大量对象但不释放
        byte[] leak = new byte[1024 * 100]; // 100KB
        // 故意不释放，模拟JNI内存泄漏
    }
    
    /**
     * 测试安全版本（正确释放资源）
     */
    private static void testSafeVersion() {
        // 模拟正确的资源管理
        try {
            byte[] temp = new byte[1024 * 100]; // 100KB
            // 正常使用后自动释放
        } catch (Exception e) {
            // 异常处理
        }
        // 资源自动释放
    }
    
    public static void main(String[] args) {
        testMemoryLeak();
    }
}
