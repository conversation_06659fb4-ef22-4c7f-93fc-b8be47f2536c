import java.net.*;
import java.io.*;

/**
 * LED连通性测试工具
 * 不依赖动态库，使用网络连接测试LED屏的连通性
 */
public class LedConnectionTest {
    
    public static void main(String[] args) {
        String ledIp = "*************";  // LED屏IP地址
        int ledPort = 5005;              // LED控制卡默认端口（常见端口）
        
        System.out.println("=== LED显示屏连通性测试工具 ===");
        System.out.println("目标设备IP: " + ledIp);
        
        // 1. 基础网络连通性测试
        testNetworkConnectivity(ledIp);
        
        // 2. 端口连通性测试
        testPortConnectivity(ledIp, ledPort);
        
        // 3. 常见LED控制卡端口扫描
        scanCommonLedPorts(ledIp);
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试基础网络连通性（ping）
     */
    public static void testNetworkConnectivity(String ip) {
        System.out.println("\n1. 测试基础网络连通性...");
        
        try {
            InetAddress address = InetAddress.getByName(ip);
            boolean reachable = address.isReachable(5000); // 5秒超时
            
            if (reachable) {
                System.out.println("✓ 网络连通性测试通过");
                System.out.println("  设备响应正常，IP: " + ip);
            } else {
                System.out.println("✗ 网络连通性测试失败");
                System.out.println("  设备无响应或不可达");
                printNetworkTroubleshooting();
            }
        } catch (IOException e) {
            System.out.println("✗ 网络测试异常: " + e.getMessage());
            printNetworkTroubleshooting();
        }
    }
    
    /**
     * 测试特定端口连通性
     */
    public static void testPortConnectivity(String ip, int port) {
        System.out.println("\n2. 测试端口连通性...");
        System.out.println("   目标端口: " + port);
        
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), 3000); // 3秒超时
            System.out.println("✓ 端口连接成功");
            System.out.println("  LED控制服务可能正在运行");
        } catch (IOException e) {
            System.out.println("✗ 端口连接失败: " + e.getMessage());
            System.out.println("  LED控制服务可能未启动或端口不正确");
        }
    }
    
    /**
     * 扫描常见LED控制卡端口
     */
    public static void scanCommonLedPorts(String ip) {
        System.out.println("\n3. 扫描常见LED控制端口...");
        
        // 常见LED控制卡端口
        int[] commonPorts = {5005, 5006, 5007, 5008, 8080, 8888, 9999, 10000, 10001};
        
        for (int port : commonPorts) {
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(ip, port), 1000); // 1秒超时
                System.out.println("✓ 发现开放端口: " + port);
            } catch (IOException e) {
                // 端口关闭，继续扫描下一个
            }
        }
    }
    
    /**
     * 打印网络故障排除建议
     */
    public static void printNetworkTroubleshooting() {
        System.out.println("\n网络故障排除建议：");
        System.out.println("1. 检查LED屏是否通电并正常启动");
        System.out.println("2. 确认LED控制卡网络配置是否正确");
        System.out.println("3. 检查网络线缆连接是否正常");
        System.out.println("4. 确认IP地址是否在同一网段");
        System.out.println("5. 检查防火墙是否阻止了连接");
        System.out.println("6. 尝试ping命令: ping *************");
    }
    
    /**
     * 高级连通性测试（模拟LED协议测试）
     */
    public static void advancedLedTest(String ip, int port) {
        System.out.println("\n4. 高级LED协议测试...");
        
        try (Socket socket = new Socket(ip, port);
             OutputStream out = socket.getOutputStream();
             InputStream in = socket.getInputStream()) {
            
            // 发送简单的测试数据包（这里只是示例）
            byte[] testData = {0x55, (byte)0xAA, 0x01, 0x00}; // 示例测试包
            out.write(testData);
            out.flush();
            
            // 等待响应
            byte[] response = new byte[1024];
            int bytesRead = in.read(response);
            
            if (bytesRead > 0) {
                System.out.println("✓ LED设备响应正常");
                System.out.println("  响应数据长度: " + bytesRead + " 字节");
            } else {
                System.out.println("✗ LED设备无响应");
            }
            
        } catch (IOException e) {
            System.out.println("✗ LED协议测试失败: " + e.getMessage());
        }
    }
}
