import java.net.*;
import java.io.*;

/**
 * 测试特定LED设备 *************
 */
public class TestSpecificLed {
    
    public static void main(String[] args) {
        String ledIp = "*************";
        
        System.out.println("=== 测试LED设备: " + ledIp + " ===");
        System.out.println();
        
        // 1. 基础连通性测试
        testBasicConnectivity(ledIp);
        
        // 2. 详细端口测试
        testLedPorts(ledIp);
        
        // 3. 尝试发送测试数据
        testLedCommunication(ledIp);
        
        // 4. 给出建议
        giveAdvice(ledIp);
    }
    
    static void testBasicConnectivity(String ip) {
        System.out.println("1. 基础连通性测试:");
        
        try {
            // Ping测试
            InetAddress address = InetAddress.getByName(ip);
            System.out.print("   Ping测试: ");
            boolean reachable = address.isReachable(5000);
            System.out.println(reachable ? "✓ 成功" : "✗ 失败");
            
            if (!reachable) {
                System.out.println("   设备无响应，可能原因:");
                System.out.println("   - LED设备未开机");
                System.out.println("   - 网络连接问题");
                System.out.println("   - IP地址不正确");
            }
            
        } catch (Exception e) {
            System.out.println("   测试异常: " + e.getMessage());
        }
        System.out.println();
    }
    
    static void testLedPorts(String ip) {
        System.out.println("2. LED控制端口测试:");
        
        // LED常用端口
        int[] ledPorts = {5005, 5006, 5007, 5008, 8080, 8888, 9999, 10000, 10001};
        String[] portDesc = {
            "主控制端口", "备用控制端口", "扩展端口1", "扩展端口2", 
            "Web管理", "配置端口", "调试端口", "数据传输", "状态监控"
        };
        
        boolean foundOpenPort = false;
        
        for (int i = 0; i < ledPorts.length; i++) {
            int port = ledPorts[i];
            String desc = portDesc[i];
            
            System.out.print("   端口 " + port + " (" + desc + "): ");
            
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(ip, port), 2000);
                System.out.println("✓ 开放");
                foundOpenPort = true;
            } catch (Exception e) {
                System.out.println("✗ 关闭");
            }
        }
        
        if (foundOpenPort) {
            System.out.println("   发现开放端口，LED设备可能在线");
        } else {
            System.out.println("   未发现开放端口，设备可能离线或端口配置不同");
        }
        System.out.println();
    }
    
    static void testLedCommunication(String ip) {
        System.out.println("3. LED通信协议测试:");
        
        // 尝试常用的LED控制端口
        int[] testPorts = {5005, 5006, 8080};
        
        for (int port : testPorts) {
            System.out.println("   测试端口 " + port + ":");
            
            try (Socket socket = new Socket()) {
                socket.setSoTimeout(3000);
                socket.connect(new InetSocketAddress(ip, port), 3000);
                
                System.out.println("     ✓ 连接成功");
                
                // 尝试发送简单的测试数据
                OutputStream out = socket.getOutputStream();
                InputStream in = socket.getInputStream();
                
                // 发送测试包（这里使用通用的测试数据）
                byte[] testData = {0x55, (byte)0xAA, 0x01, 0x00, 0x01, (byte)0xFF};
                out.write(testData);
                out.flush();
                
                System.out.println("     ✓ 测试数据已发送");
                
                // 等待响应
                byte[] buffer = new byte[1024];
                socket.setSoTimeout(2000);
                
                try {
                    int bytesRead = in.read(buffer);
                    if (bytesRead > 0) {
                        System.out.println("     ✓ 收到响应: " + bytesRead + " 字节");
                        System.out.println("     这可能是一个LED控制设备！");
                    } else {
                        System.out.println("     - 无响应数据");
                    }
                } catch (SocketTimeoutException e) {
                    System.out.println("     - 响应超时");
                }
                
                break; // 如果连接成功就不测试其他端口了
                
            } catch (Exception e) {
                System.out.println("     ✗ 连接失败: " + e.getMessage());
            }
        }
        System.out.println();
    }
    
    static void giveAdvice(String ip) {
        System.out.println("4. 诊断建议:");
        System.out.println("   目标设备: " + ip);
        System.out.println();
        
        System.out.println("   如果设备无法连接，请检查:");
        System.out.println("   □ LED显示屏是否通电");
        System.out.println("   □ LED控制卡指示灯状态");
        System.out.println("   □ 网线连接是否正常");
        System.out.println("   □ 控制卡网络配置是否正确");
        System.out.println();
        
        System.out.println("   LED控制卡常见默认配置:");
        System.out.println("   - 默认IP: *************, *************");
        System.out.println("   - 控制端口: 5005, 5006");
        System.out.println("   - Web管理: 8080");
        System.out.println();
        
        System.out.println("   建议操作:");
        System.out.println("   1. 使用厂商提供的LED配置软件");
        System.out.println("   2. 检查控制卡说明书确认正确IP和端口");
        System.out.println("   3. 尝试重启LED控制卡");
        System.out.println("   4. 联系LED设备厂商技术支持");
    }
}
