package com.listenvision;

import com.dpos.gap.common.redis.service.GapRedisService;
import com.dpos.gap.common.utils.StringUtils;
import com.dpos.model.gate.LedLsParamVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 类描述：
 * <AUTHOR>
 * @date 2025/6/9 15:57
 */
@Service
public class LedlsService {
    private static final Logger log = LoggerFactory.getLogger(LedlsService.class);
    @Autowired
    private GapRedisService gapRedisService;

    @PostConstruct
    public void init()
    {

    	try {
    	    String OS = System.getProperty("os.name").toLowerCase();
            if (OS.contains("win")){
                System.loadLibrary("lv_led");
            }else if (OS.contains("nix") || OS.contains("nux") || OS.contains("aix")){
                System.load("/usr/local/lib/libledplayer7.so");
            }

			//Bx6GEnv.initial(30000);
            //String OS = System.getProperty("os.name").toLowerCase();
            if (OS.contains("win")){
                //led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
            }else if (OS.contains("nix") || OS.contains("nux") || OS.contains("aix")) {
                led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    private static final String SPECIAL_CHARS = "!@#$%^&*()_+[]{}|;:'\",.<>?/~`-=\\";
    public static boolean containsSpecialChar(String input) {
        // 遍历字符串中的每个字符
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            // 判断当前字符是否在特殊字符集合中
            if (SPECIAL_CHARS.indexOf(c) != -1) {
                // 如果找到特殊字符，则返回true
                return true;
            }
        }
        // 如果遍历完所有字符都未找到特殊字符，则返回false
        return false;
    }
    /**
     * 灵信发送
     * @param ledLsParam
     * @param ip
     * @param sendContent
     */
    //@Async
    public void sendLedLs(LedLsParamVO ledLsParam, String ip, String sendContent){
        log.info("LED 灵信发送:{},{}",ip,sendContent);
        String blackIpList=gapRedisService.getCacheObject("gap_led_black_ip");
        if(StringUtils.isNotBlank(blackIpList) && blackIpList.contains(ip)){
            log.info("LED 灵信发送:黑名单IP,{}",ip);
            return;
        }
        sendContent=sendContent.replaceAll("\\(","").replaceAll("\\)","");
//        if(containsSpecialChar(sendContent)){
//            log.info("LED内容还有特殊字符,{}",sendContent);
//            return;
//        }
        /*if(true){
            return;
        }*/
        long hProgram =0;
        try {
            int online=led.TestOnline(ip);
            log.info("LED-灵信发送,通讯:{}",online);
            if(online==0){
                led.InitLedRgb(0); //模组的RGB顺序,仅C卡有效,其他卡固定为0. C卡时, 0:  R->G->B 1: G->R->B 2:R->B->G 3:B->R->G 4:B->G->R 5:G->B->R
                //添加一个多行文本到图文区
                //SaveType		节目保存位置，默认为0保存为flash节目，3保存为ram节目。注：flash节目掉电不清除，ram节目掉电清除。应用场景需要实时刷新的，建议保持为ram节目
                //目前仅C卡程序才支持切换,  其他卡默认出货为flash程序,如果需要RAM程序请联系业务或者在官网下载,然后使用Led Player对卡进行升级
                hProgram = led.CreateProgram(ledLsParam.getLedWidth(), ledLsParam.getLedHeight(), ledLsParam.getColorType(), ledLsParam.getGrayLevel(), 0);
                log.info("LED-灵信发送,创建节目对象:{}",hProgram);
                int addProgram=led.AddProgram(hProgram, 0, 0, 1);
                int addResult= led.AddImageTextArea(hProgram, 0, 1, 0, 0, ledLsParam.getLedWidth(), ledLsParam.getLedHeight(), 1);
                int addResult1=led.AddMultiLineTextToImageTextArea(hProgram, 0, 1, 0, sendContent,
                        ledLsParam.getFontName(), ledLsParam.getFontSize(), ledLsParam.getFontColor(), ledLsParam.getFontBold(), ledLsParam.getFontItalic(),
                         ledLsParam.getFontUnderline(), ledLsParam.getInStyle(), ledLsParam.getNspeed(), ledLsParam.getDelayTime(),
                        ledLsParam.getAlignment(), ledLsParam.getIsVcenter());

                //log.info("LED-灵信添加节目:{},添加图文区域:{},添加一个多行文本到图文区:{}",addProgram,addResult,addResult1);
                int errCode = led.NetWorkSend(ip, hProgram);
                log.info("LED-灵信发送结果:{}",errCode);
                //led.DeleteProgram(hProgram);
            }
        }catch (Exception e){
            log.error("LED 灵信发送失败:IP:{},发送内容:{},错误：{}",ip,sendContent,e.toString());
//            String OS = System.getProperty("os.name").toLowerCase();
//            if (OS.contains("win")){
//                //led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
//            }else if (OS.contains("nix") || OS.contains("nux") || OS.contains("aix")) {
//                led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
//            }
        }finally {
            if(hProgram!=0) {
                try {
                    log.info("LED 灵信发送删除节目:{}",hProgram);
                    led.DeleteProgram(hProgram);
                }catch (Exception e){

                }
            }
        }
    }


}