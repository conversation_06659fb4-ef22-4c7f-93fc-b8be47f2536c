import com.listenvision.led;
import com.listenvision.ledCallBack;

public class test {

    public static void main(String[] args) {
		

        String strIp = "*************";
        int ledWidth = 64;//屏的宽度
        int ledHeight = 64;//屏的高度
        int colorType = 3;//屏的颜色 1.单色  2.双基色  3.三基色   注：C卡全彩参数为3      X系列卡参数固定为 4
        int grayLevel =0;//灰度等级  赋值  1-5对应的灰度等级分别为 无,4,8,16,32   除C卡外，其它卡传0


        //第一步初始化LED类型
        led.InitLedType(0);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C

				//led.InitLedRgb(0); //模组的RGB顺序,仅C卡有效,其他卡固定为0. C卡时, 0:  R->G->B 1: G->R->B 2:R->B->G 3:B->R->G 4:B->G->R 5:G->B->R

        //Example 1    Basic screen parameter settings（设置基本屏参）
        //onSetBasicInfo(strIp,ledWidth,ledHeight,colorType,grayLevel);

        //Example 2    Switch screen（开关屏）
        //onPowerOnOff(strIp);

        //Example 3    Calibration time（校时)
        //onAdjustTime(strIp);

        //Example 4    Single line text program（一个节目下只有一个连接左移的单行文本区）
       // onSingleLineText(strIp,ledWidth,ledHeight,colorType,grayLevel);

        //Example 5    Multi-line text program（一个节目下只有一个多行文本区）
       // onMultiLineText(strIp,ledWidth,ledHeight,colorType,grayLevel);

        //Example 6    Picture program（一个节目下只有一个图片区）
        //onPicture(strIp,ledWidth,ledHeight,colorType,grayLevel);

        //Example 7    A single-line text area and a picture area program(一个节目下有一个连续上移的单行文本区和一个图片区)
        //onTwoAreas(strIp,ledWidth,ledHeight,colorType,grayLevel);

        //Example 8    A single-line text area program and a digital clock area program（两个节目下各有一个单行文本区和一个数字时钟区）
        //onTwoPrograms(strIp,ledWidth,ledHeight,colorType,grayLevel);
       
        //There are two lines of text under one program（一个节目下有两个多行文本,并且有背景图片）
    onTwoMutiTextAndBackground(strIp,ledWidth,ledHeight,colorType,grayLevel);
    
    //There are two lines of text under one program（一个节目下有两个多行文本）
    //onTwoMutiText (strIp,ledWidth,ledHeight,colorType,grayLevel);
    
    //A program has an inside code and a background（一个节目下有内码和背景）     
    //内码必须先加载字库  The inner code must first load the font library
    //onNeiMaAnBackground(strIp,ledWidth,ledHeight,colorType,grayLevel);

    //The internal code is partially refreshed(内码局部刷新)
    //onRefreshNeiMa(strIp);
        /***************广域网开发简单示例 C2M C4M型号才支持*************/
        //打开服务端口
        // ledCallBack cb = new ledCallBack()
        // {
        //     //上下线只会通知一次
        //     public void LedServerCallback(int Msg, int wParam, String networkIdStr)
        //     {
        //         switch(Msg){
        //             case 1://控制卡连上
        //                 System.out.println("connected ID："+networkIdStr);
		// 				onSingleLineText(networkIdStr,ledWidth,ledHeight,colorType,grayLevel);
        //                 break;
        //             case 2://控制卡断开
        //                 System.out.println("disconnect ID："+networkIdStr);
        //                 break;
        //             default :
        //                 break;
        //         }
        //     }
        // };
        // led.LedInitServer(10012);
        // led.RegisterLedServerCallback(cb);

        // try {
        //     Thread.sleep(30000);
        // } catch (InterruptedException e) {
        //     e.printStackTrace(); 
        // }

        // //断开服务
        // led.LedShudownServer();

        /*******************************************************/
    }

    //Basic screen parameter settings（设置基本屏参）
    public static void onSetBasicInfo(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        int res = led.SetBasicInfo(strIp,colorType,grayLevel,ledWidth,ledHeight);
        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("设置基本屏参成功");
    }

    //Switch screen（开关屏）
    public static void onPowerOnOff(String strIp)
    {
        int res = led.PowerOnOff(strIp,0);
        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("开关屏成功");
    }

    //Calibration time（校时）
    public static void onAdjustTime(String strIp)
    {
        int res = led.AdjustTime(strIp);
        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("校时成功");
    }

    //Single line text program（一个节目下只有一个连接左移的单行文本区）
    public static void onSingleLineText(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0);
        led.AddProgram(hProgram, 0, 0, 1);
        led.AddImageTextArea(hProgram, 0, 1, 0, 0, ledWidth, ledHeight, 1);
        led.AddSinglelineTextToImageTextArea(hProgram,0,1,0,"Shanghai Listen Vision Technology Inc.","./font/simsun.ttc",20,0xff,0,0,0,6,10,0);
        int res = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);

        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }

    //Multi-line text program（一个节目下只有一个多行文本区）
    public static void onMultiLineText(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0);
        led.AddProgram(hProgram, 0, 0, 1);
        led.AddImageTextArea(hProgram, 0, 1, 0, 0, 10, 16, 1);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
        led.AddMultiLineTextToImageTextArea(hProgram,0,1,0,"A","./font/simsun.ttc",12,0xff,0,0,0,0,4,10,0,0);
        //led.AddMultiLineTextToImageTextArea(hProsram,0,1,0,"上海灵信视觉2","simsun.ttc",14,0xff,0,0,0,0,10,5,0,0);
        //led.AddMultiLineTextToImageTextArea(hProgram,0,1,0,"上海灵信视觉3","simsun.ttc",14,0xff,0,0,0,0,10,5,0,0);
        int res = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);
        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }

    //Picture program（一个节目下只有一个图片区）
    public static void onPicture(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0);
        led.AddProgram(hProgram, 0, 0, 1);
        led.AddImageTextArea(hProgram, 0, 1, 0, 0, ledWidth, ledHeight, 1);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
        led.AddFileToImageTextArea(hProgram, 0, 1, "test.bmp", 1, 4, 2);
        //led.AddFileToImageTextArea(hProgram, 0, 1, "test.jpg", 1, 4, 2);
        int res = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);
        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }

    //A single-line text area and a picture area program(一个节目下有一个连续上移的单行文本区和一个图片区)
    public static void onTwoAreas(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0);
        led.AddProgram(hProgram, 0, 0, 1);
        led.AddImageTextArea(hProgram, 0, 1, 0, 0, ledWidth, ledHeight/2, 1);
        led.AddSinglelineTextToImageTextArea(hProgram,0,1,0,"Listen","./font/simsun.ttc",20,0xff,0,0,0,8,20,0);
        led.AddImageTextArea(hProgram, 0, 2, 0, ledHeight/2, ledWidth, ledHeight/2, 1);
        led.AddFileToImageTextArea(hProgram, 0, 2, "test.bmp", 1, 4, 2);
        int res = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);

        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }

    //A single-line text area program and a digital clock area program（两个节目下各有一个单行文本区和一个数字时钟区）
    public static void onTwoPrograms(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0);

        //添加单行文本节目
        led.AddProgram(hProgram, 0, 0, 1);
        led.AddImageTextArea(hProgram, 0, 1, 0, 0, ledWidth, ledHeight, 1);
        led.AddSinglelineTextToImageTextArea(hProgram,0,1,0,"Shanghai Listen Vision Technology Inc.","./font/simsun.ttc",20,0xff,0,0,0,6,10,0);
        //添加数字时钟节目
        led.AddProgram(hProgram, 1, 0, 1);
        led.AddDigitalClockArea(hProgram,1,1,0,0,ledWidth,ledHeight,"./font/simsun.ttc",10,0xff,0,0,0,0,0,0,0,1,1,1,0,0xff,0,0xff,0,0xff,1);

        int res = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);

        if(res != 0)
        {
            String errStr = led.GetErrorCodeInfo(res);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }
    
    
   //There are two lines of text under one program（一个节目下有两个多行文本）
    public static void onTwoMutiText(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0);

        //添加文本节目 
        int nProgramId=0;
        int nAreaId=1;
        led.AddProgram(hProgram, nProgramId, 0, 1);
        //第一个区域 the first area
        led.AddImageTextArea(hProgram, nProgramId, nAreaId, 0, 0, ledWidth, ledHeight/2, 1);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
        led.AddMultiLineTextToImageTextArea(hProgram,nProgramId, nAreaId,0,"上海灵信视觉1","./font/simsun.ttc",12,0xff,1,0,0,0,4,10,0,0);

		//第二个区域 the second area
		nAreaId++;
        led.AddImageTextArea(hProgram, nProgramId, nAreaId, 0, ledHeight/2, ledWidth, ledHeight/2, 1);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
        led.AddMultiLineTextToImageTextArea(hProgram,nProgramId, nAreaId,0,"上海灵信视觉2","./font/simsun.ttc",12,0xff,1,0,0,0,4,10,0,0);

        int errCode = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);

        if(errCode != 0)
        {
            String errStr = led.GetErrorCodeInfo(errCode);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }
    
       //There are two lines of text under one program（一个节目下有两个多行文本,并且有背景图片）
    public static void onTwoMutiTextAndBackground(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
         System.out.println("onTwoMutiTextAndBackground" );
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0);

        //添加文本节目 
        int nProgramId=0;
        int nAreaId=1;
        led.AddProgram(hProgram, nProgramId, 0, 1);
        //背景图片区域,你可以根据需要去更换  Background image area, you can change as needed
        led.AddImageTextArea(hProgram, nProgramId, nAreaId, 0, 0, ledWidth, ledHeight, 0);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
        int errCode = led.AddFileToImageTextArea(hProgram, nProgramId, nAreaId, "back.bmp", 0, 4, 65535);
        if(errCode != 0)
        {
            String errStr = led.GetErrorCodeInfo(errCode);
            System.out.println("失败：" + errStr);
            return;
        }
        
        //第一个区域 the first area
        nAreaId++;
        led.AddImageTextArea(hProgram, nProgramId, nAreaId, 0, 1, ledWidth-2, ledHeight/2-2, 1);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
        errCode = led.AddMultiLineTextToImageTextArea(hProgram,nProgramId, nAreaId,0,"文本001","./font/simsun.ttc",12,0xff,1,0,0,0,4,10,0,0);
        if(errCode != 0)
        {
            String errStr = led.GetErrorCodeInfo(errCode);
            System.out.println("失败：" + errStr);
            return;
        }
		//第二个区域 the second area
		nAreaId++;
        led.AddImageTextArea(hProgram, nProgramId, nAreaId, 0, ledHeight/2+1, ledWidth, ledHeight/2-2, 1);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
       errCode =  led.AddMultiLineTextToImageTextArea(hProgram,nProgramId, nAreaId,0,"文本002","./font/simsun.ttc",12,0xff,1,0,0,0,4,10,0,0);
       if(errCode != 0)
        {
            String errStr = led.GetErrorCodeInfo(errCode);
            System.out.println("失败：" + errStr);
            return;
        }


        errCode = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);

        if(errCode != 0)
        {
            String errStr = led.GetErrorCodeInfo(errCode);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送一个节目下有两个多行文本,并且有背景图片 成功");
    }
    //A program has an inside code and a background（一个节目下有内码和背景）
    //内码必须先加载字库  The inner code must first load the font library
    public static void onNeiMaAnBackground(String strIp,int ledWidth,int ledHeight,int colorType,int grayLevel)
    {
        System.out.println("一个节目下有内码和背景");
        long hProgram = led.CreateProgram(ledWidth, ledHeight, colorType,grayLevel,0); 
        //添加文本节目 
        int nProgramId=0;
        int nAreaId=1;
        led.AddProgram(hProgram, nProgramId, 0, 1);
        //背景图片区域,你可以根据需要去更换  Background image area, you can change as needed
        led.AddImageTextArea(hProgram, nProgramId, nAreaId, 0, 0, ledWidth, ledHeight/2, 1);
        //You can add multiple sub-items to the graphic area(可以添加多个子项到图文区，如下添加可以选一个或多个添加)
        led.AddFileToImageTextArea(hProgram, nProgramId, nAreaId, "back.bmp", 0, 4, 65535);
        
        //添加内码区域  Add an internal code area
        //C卡的left和top可以任意  其他类型的卡的必须为8的整数倍.  所有卡的区域大小不得超过屏总大小
        //The left and top values of card C can be arbitrary. The values of other types of cards must be an integer multiple of 8. The area size of all cards must not exceed the total screen size
        nAreaId++;
        led.AddNeiMaArea(hProgram,nProgramId,nAreaId,1,1,ledWidth-2,ledHeight/2-2,"001",16,0xff,0,10,255);
        //后面可以继续添加 You can add more later
        //nAreaId++;
        //led.AddNeiMaArea(hProgram,nProgramId,nAreaId,0,ledHeight/2,ledWidth,ledHeight/2,"002",16,0xff,0,10,255);
        
        int errCode = led.NetWorkSend(strIp, hProgram);
        led.DeleteProgram(hProgram);

        if(errCode != 0)
        {
            String errStr = led.GetErrorCodeInfo(errCode);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }
   public static void onRefreshNeiMa(String strIp)
    {
    	String str1="%disp0:1NeiMa测试123456";//格式可以查看<<内码区域局部更新协议>>文档
    	int errCode = led.RefreshNeiMaArea(strIp, str1);


        if(errCode != 0)
        {
            String errStr = led.GetErrorCodeInfo(errCode);
            System.out.println("失败：" + errStr);
        }
        else
            System.out.println("发送节目成功");
    }
}

