import java.net.*;
import java.io.*;
import java.util.*;

/**
 * LED设备诊断报告生成器
 */
public class LedDiagnosticReport {
    
    public static void main(String[] args) {
        String targetIp = "*************";
        
        System.out.println("=== LED设备诊断报告 ===");
        System.out.println("目标设备: " + targetIp);
        System.out.println("诊断时间: " + new Date());
        System.out.println("==========================================");
        
        // 1. 网络层诊断
        networkDiagnosis(targetIp);
        
        // 2. 传输层诊断
        transportDiagnosis(targetIp);
        
        // 3. 应用层诊断
        applicationDiagnosis(targetIp);
        
        // 4. 生成诊断结论
        generateConclusion(targetIp);
    }
    
    static void networkDiagnosis(String ip) {
        System.out.println("\n1. 网络层诊断 (Network Layer):");
        System.out.println("   测试项目: ICMP连通性");
        
        try {
            InetAddress address = InetAddress.getByName(ip);
            long startTime = System.currentTimeMillis();
            boolean reachable = address.isReachable(5000);
            long responseTime = System.currentTimeMillis() - startTime;
            
            if (reachable) {
                System.out.println("   ✓ ICMP响应正常");
                System.out.println("   响应时间: " + responseTime + "ms");
                System.out.println("   设备在线，网络层连通");
            } else {
                System.out.println("   ✗ ICMP无响应");
                System.out.println("   可能原因: 设备离线、防火墙阻止ICMP、设备不支持ping");
            }
            
            // DNS解析测试
            String hostname = address.getHostName();
            if (!hostname.equals(ip)) {
                System.out.println("   DNS解析: " + hostname);
            } else {
                System.out.println("   DNS解析: 无域名记录");
            }
            
        } catch (Exception e) {
            System.out.println("   ✗ 网络诊断异常: " + e.getMessage());
        }
    }
    
    static void transportDiagnosis(String ip) {
        System.out.println("\n2. 传输层诊断 (Transport Layer):");
        System.out.println("   测试项目: TCP端口连通性");
        
        // LED设备常用端口
        Map<Integer, String> ledPorts = new LinkedHashMap<>();
        ledPorts.put(5005, "LED主控制端口");
        ledPorts.put(5006, "LED备用控制端口");
        ledPorts.put(5007, "LED扩展端口1");
        ledPorts.put(5008, "LED扩展端口2");
        ledPorts.put(8080, "Web管理界面");
        ledPorts.put(8888, "配置管理端口");
        ledPorts.put(23, "Telnet远程管理");
        ledPorts.put(80, "HTTP服务");
        
        int openPorts = 0;
        int totalPorts = ledPorts.size();
        
        for (Map.Entry<Integer, String> entry : ledPorts.entrySet()) {
            int port = entry.getKey();
            String description = entry.getValue();
            
            try (Socket socket = new Socket()) {
                long startTime = System.currentTimeMillis();
                socket.connect(new InetSocketAddress(ip, port), 3000);
                long connectTime = System.currentTimeMillis() - startTime;
                
                System.out.println("   ✓ 端口 " + port + " 开放 (" + description + ") - " + connectTime + "ms");
                openPorts++;
                
            } catch (Exception e) {
                System.out.println("   ✗ 端口 " + port + " 关闭 (" + description + ")");
            }
        }
        
        System.out.println("   端口开放率: " + openPorts + "/" + totalPorts + 
                          " (" + (openPorts * 100 / totalPorts) + "%)");
        
        if (openPorts == 0) {
            System.out.println("   诊断: 设备可能未运行LED控制服务");
        } else {
            System.out.println("   诊断: 设备运行了 " + openPorts + " 个服务");
        }
    }
    
    static void applicationDiagnosis(String ip) {
        System.out.println("\n3. 应用层诊断 (Application Layer):");
        System.out.println("   测试项目: LED控制协议通信");
        
        // 尝试LED控制协议通信
        int[] ledControlPorts = {5005, 5006, 5007, 5008};
        boolean foundLedService = false;
        
        for (int port : ledControlPorts) {
            System.out.println("   测试端口 " + port + ":");
            
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(ip, port), 3000);
                socket.setSoTimeout(2000);
                
                OutputStream out = socket.getOutputStream();
                InputStream in = socket.getInputStream();
                
                // 发送LED测试命令（模拟）
                byte[] testCommand = {0x55, (byte)0xAA, 0x01, 0x00, 0x01, (byte)0xFF};
                out.write(testCommand);
                out.flush();
                
                // 等待响应
                byte[] buffer = new byte[1024];
                try {
                    int bytesRead = in.read(buffer);
                    if (bytesRead > 0) {
                        System.out.println("     ✓ 收到响应: " + bytesRead + " 字节");
                        System.out.println("     可能是LED控制设备");
                        foundLedService = true;
                    } else {
                        System.out.println("     - 连接成功但无数据响应");
                    }
                } catch (SocketTimeoutException e) {
                    System.out.println("     - 连接成功但响应超时");
                }
                
            } catch (Exception e) {
                System.out.println("     ✗ 连接失败: " + e.getMessage());
            }
        }
        
        if (!foundLedService) {
            System.out.println("   诊断: 未发现LED控制协议响应");
        }
        
        // 尝试HTTP服务
        testHttpService(ip);
    }
    
    static void testHttpService(String ip) {
        System.out.println("   测试HTTP服务:");
        
        int[] httpPorts = {80, 8080, 8888};
        
        for (int port : httpPorts) {
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(ip, port), 2000);
                
                OutputStream out = socket.getOutputStream();
                InputStream in = socket.getInputStream();
                
                // 发送HTTP GET请求
                String httpRequest = "GET / HTTP/1.1\r\nHost: " + ip + "\r\n\r\n";
                out.write(httpRequest.getBytes());
                out.flush();
                
                // 读取响应
                byte[] buffer = new byte[1024];
                socket.setSoTimeout(2000);
                
                try {
                    int bytesRead = in.read(buffer);
                    if (bytesRead > 0) {
                        String response = new String(buffer, 0, Math.min(bytesRead, 200));
                        System.out.println("     ✓ 端口 " + port + " HTTP响应:");
                        System.out.println("       " + response.split("\r\n")[0]);
                    }
                } catch (SocketTimeoutException e) {
                    System.out.println("     - 端口 " + port + " HTTP响应超时");
                }
                
            } catch (Exception e) {
                // HTTP端口不可用
            }
        }
    }
    
    static void generateConclusion(String ip) {
        System.out.println("\n4. 诊断结论:");
        System.out.println("==========================================");
        
        try {
            InetAddress address = InetAddress.getByName(ip);
            boolean networkReachable = address.isReachable(3000);
            
            if (networkReachable) {
                System.out.println("✓ 设备在线: " + ip);
                System.out.println("✗ LED控制服务: 未检测到标准LED控制协议");
                
                System.out.println("\n可能的情况:");
                System.out.println("1. 这是一个普通网络设备，不是LED控制器");
                System.out.println("2. LED控制器使用非标准端口或协议");
                System.out.println("3. LED控制软件未启动");
                System.out.println("4. 需要特定的认证或握手过程");
                
                System.out.println("\n建议操作:");
                System.out.println("1. 确认设备类型和型号");
                System.out.println("2. 查阅设备说明书确认正确的IP和端口");
                System.out.println("3. 使用厂商提供的专用配置软件");
                System.out.println("4. 联系设备厂商技术支持");
                
            } else {
                System.out.println("✗ 设备离线: " + ip);
                System.out.println("\n建议检查:");
                System.out.println("1. 设备是否通电");
                System.out.println("2. 网络连接是否正常");
                System.out.println("3. IP地址是否正确");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 诊断过程出现异常: " + e.getMessage());
        }
        
        System.out.println("\n==========================================");
        System.out.println("诊断完成时间: " + new Date());
    }
}
