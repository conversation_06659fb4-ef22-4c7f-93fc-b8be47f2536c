import com.listenvision.led;

/**
 * LED程序资源管理器 - 防止内存泄漏
 * 使用try-with-resources模式确保资源正确释放
 */
public class LedProgramManager implements AutoCloseable {
    private long hProgram;
    private boolean closed = false;
    private final String debugInfo;
    
    /**
     * 创建LED程序管理器
     */
    public LedProgramManager(int width, int height, int colorType, int grayLevel, int saveType) {
        this.debugInfo = String.format("Program[%dx%d, color=%d, gray=%d]", width, height, colorType, grayLevel);
        this.hProgram = led.CreateProgram(width, height, colorType, grayLevel, saveType);
        
        if (this.hProgram == 0) {
            throw new RuntimeException("Failed to create LED program: " + debugInfo);
        }
        
        System.out.println("✓ Created " + debugInfo + " with handle: " + hProgram);
    }
    
    /**
     * 获取程序句柄
     */
    public long getHandle() {
        if (closed) {
            throw new IllegalStateException("Program already closed: " + debugInfo);
        }
        return hProgram;
    }
    
    /**
     * 检查程序是否已关闭
     */
    public boolean isClosed() {
        return closed;
    }
    
    /**
     * 获取调试信息
     */
    public String getDebugInfo() {
        return debugInfo;
    }
    
    /**
     * 手动关闭资源（通常不需要调用，try-with-resources会自动调用）
     */
    @Override
    public void close() {
        if (!closed && hProgram != 0) {
            try {
                led.DeleteProgram(hProgram);
                System.out.println("✓ Deleted " + debugInfo + " with handle: " + hProgram);
            } catch (Exception e) {
                System.err.println("✗ Error deleting " + debugInfo + ": " + e.getMessage());
            } finally {
                closed = true;
                hProgram = 0;
            }
        }
    }
    
    /**
     * 确保资源被释放（垃圾回收时的最后保障）
     */
    @Override
    protected void finalize() throws Throwable {
        if (!closed) {
            System.err.println("⚠ Warning: " + debugInfo + " was not properly closed, forcing cleanup");
            close();
        }
        super.finalize();
    }
    
    /**
     * 添加程序到LED程序管理器
     */
    public int addProgram(int programNo, int programTime, int loopCount) {
        return led.AddProgram(getHandle(), programNo, programTime, loopCount);
    }
    
    /**
     * 添加图文区域
     */
    public int addImageTextArea(int programNo, int areaNo, int l, int t, int w, int h, int layout) {
        return led.AddImageTextArea(getHandle(), programNo, areaNo, l, t, w, h, layout);
    }
    
    /**
     * 添加文件到图文区域
     */
    public int addFileToImageTextArea(int programNo, int areaNo, String filePath, int inStyle, int speed, int delayTime) {
        return led.AddFileToImageTextArea(getHandle(), programNo, areaNo, filePath, inStyle, speed, delayTime);
    }
    
    /**
     * 添加多行文本到图文区域
     */
    public int addMultiLineTextToImageTextArea(int programNo, int areaNo, int addType, String addStr, 
            String fontName, int fontSize, int fontColor, int fontBold, int fontItalic, int fontUnderline,
            int inStyle, int speed, int delayTime, int alignment, int isVCenter) {
        return led.AddMultiLineTextToImageTextArea(getHandle(), programNo, areaNo, addType, addStr, 
                fontName, fontSize, fontColor, fontBold, fontItalic, fontUnderline, 
                inStyle, speed, delayTime, alignment, isVCenter);
    }
    
    /**
     * 添加单行文本到图文区域
     */
    public int addSinglelineTextToImageTextArea(int programNo, int areaNo, int addType, String addStr,
            String fontName, int fontSize, int fontColor, int fontBold, int fontItalic, int fontUnderline,
            int inStyle, int speed, int delayTime) {
        return led.AddSinglelineTextToImageTextArea(getHandle(), programNo, areaNo, addType, addStr,
                fontName, fontSize, fontColor, fontBold, fontItalic, fontUnderline, inStyle, speed, delayTime);
    }
    
    /**
     * 发送程序到LED设备
     */
    public int sendToDevice(String ipStr) {
        return led.NetWorkSend(ipStr, getHandle());
    }
    
    /**
     * 通过串口发送程序
     */
    public int sendToSerialPort(int commPort, int baud, int ledNumber) {
        return led.SerialPortSend(commPort, baud, ledNumber, (int)getHandle());
    }
}
