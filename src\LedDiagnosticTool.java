import java.net.*;
import java.io.*;
import java.util.*;

/**
 * LED显示屏连通性诊断工具
 * 提供全面的网络和设备连通性检测
 */
public class LedDiagnosticTool {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== LED显示屏连通性诊断工具 ===");
        
        // 获取用户输入的LED IP地址
        System.out.print("请输入LED屏IP地址 (默认: *************): ");
        String input = scanner.nextLine().trim();
        String ledIp = input.isEmpty() ? "*************" : input;
        
        System.out.println("\n开始诊断LED设备: " + ledIp);
        System.out.println("==========================================");
        
        // 1. 本机网络信息
        printLocalNetworkInfo();
        
        // 2. 网段分析
        analyzeNetworkSegment(ledIp);
        
        // 3. 基础连通性测试
        testBasicConnectivity(ledIp);
        
        // 4. LED端口扫描
        scanLedPorts(ledIp);
        
        // 5. 提供解决方案
        provideSolutions(ledIp);
        
        scanner.close();
    }
    
    /**
     * 打印本机网络信息
     */
    public static void printLocalNetworkInfo() {
        System.out.println("\n1. 本机网络信息:");
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                if (ni.isUp() && !ni.isLoopback()) {
                    Enumeration<InetAddress> addresses = ni.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress addr = addresses.nextElement();
                        if (addr instanceof Inet4Address) {
                            System.out.println("   网卡: " + ni.getDisplayName());
                            System.out.println("   IP地址: " + addr.getHostAddress());
                        }
                    }
                }
            }
        } catch (SocketException e) {
            System.out.println("   获取网络信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析网段
     */
    public static void analyzeNetworkSegment(String ledIp) {
        System.out.println("\n2. 网段分析:");
        
        try {
            // 获取本机IP
            String localIp = getLocalIP();
            System.out.println("   本机IP: " + localIp);
            System.out.println("   LED IP: " + ledIp);
            
            // 分析网段
            String localSegment = getNetworkSegment(localIp);
            String ledSegment = getNetworkSegment(ledIp);
            
            System.out.println("   本机网段: " + localSegment);
            System.out.println("   LED网段: " + ledSegment);
            
            if (localSegment.equals(ledSegment)) {
                System.out.println("   ✓ 网段匹配，可以直接通信");
            } else {
                System.out.println("   ✗ 网段不匹配，需要路由或修改网络配置");
            }
            
        } catch (Exception e) {
            System.out.println("   网段分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试基础连通性
     */
    public static void testBasicConnectivity(String ledIp) {
        System.out.println("\n3. 基础连通性测试:");
        
        try {
            InetAddress address = InetAddress.getByName(ledIp);
            
            // 测试可达性
            System.out.print("   测试网络可达性... ");
            boolean reachable = address.isReachable(5000);
            
            if (reachable) {
                System.out.println("✓ 通过");
            } else {
                System.out.println("✗ 失败");
            }
            
            // DNS解析测试
            System.out.print("   DNS解析测试... ");
            String hostname = address.getHostName();
            if (!hostname.equals(ledIp)) {
                System.out.println("✓ 解析为: " + hostname);
            } else {
                System.out.println("- 无域名解析");
            }
            
        } catch (IOException e) {
            System.out.println("   连通性测试异常: " + e.getMessage());
        }
    }
    
    /**
     * 扫描LED常用端口
     */
    public static void scanLedPorts(String ledIp) {
        System.out.println("\n4. LED端口扫描:");
        
        // 常见LED控制卡端口及其说明
        Map<Integer, String> ledPorts = new HashMap<>();
        ledPorts.put(5005, "通用LED控制端口");
        ledPorts.put(5006, "备用控制端口");
        ledPorts.put(5007, "扩展控制端口");
        ledPorts.put(5008, "高级功能端口");
        ledPorts.put(8080, "Web管理端口");
        ledPorts.put(8888, "配置管理端口");
        ledPorts.put(9999, "调试端口");
        ledPorts.put(10000, "数据传输端口");
        ledPorts.put(10001, "状态监控端口");
        
        boolean foundOpenPort = false;
        
        for (Map.Entry<Integer, String> entry : ledPorts.entrySet()) {
            int port = entry.getKey();
            String description = entry.getValue();
            
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(ledIp, port), 2000);
                System.out.println("   ✓ 端口 " + port + " 开放 - " + description);
                foundOpenPort = true;
            } catch (IOException e) {
                System.out.println("   - 端口 " + port + " 关闭 - " + description);
            }
        }
        
        if (!foundOpenPort) {
            System.out.println("   ✗ 未发现开放的LED控制端口");
        }
    }
    
    /**
     * 提供解决方案
     */
    public static void provideSolutions(String ledIp) {
        System.out.println("\n5. 解决方案建议:");
        
        try {
            String localIp = getLocalIP();
            String localSegment = getNetworkSegment(localIp);
            String ledSegment = getNetworkSegment(ledIp);
            
            if (!localSegment.equals(ledSegment)) {
                System.out.println("\n   网段不匹配解决方案:");
                System.out.println("   方案1: 修改LED屏IP地址");
                System.out.println("          建议IP: " + localSegment + ".116");
                System.out.println("   方案2: 修改本机IP地址");
                System.out.println("          建议IP: " + ledSegment + ".100");
                System.out.println("   方案3: 配置路由器转发");
            }
            
            System.out.println("\n   通用故障排除步骤:");
            System.out.println("   1. 确认LED屏电源已开启");
            System.out.println("   2. 检查网线连接是否牢固");
            System.out.println("   3. 确认LED控制卡网络配置");
            System.out.println("   4. 检查防火墙设置");
            System.out.println("   5. 尝试重启LED控制卡");
            System.out.println("   6. 使用LED厂商提供的配置工具");
            
            System.out.println("\n   LED控制卡常见默认配置:");
            System.out.println("   - 默认IP: ************* 或 *************");
            System.out.println("   - 默认端口: 5005");
            System.out.println("   - 默认用户名/密码: admin/admin");
            
        } catch (Exception e) {
            System.out.println("   生成解决方案时出错: " + e.getMessage());
        }
    }
    
    /**
     * 获取本机IP地址
     */
    private static String getLocalIP() throws SocketException {
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface ni = interfaces.nextElement();
            if (ni.isUp() && !ni.isLoopback()) {
                Enumeration<InetAddress> addresses = ni.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (addr instanceof Inet4Address && !addr.isLoopbackAddress()) {
                        return addr.getHostAddress();
                    }
                }
            }
        }
        return "127.0.0.1";
    }
    
    /**
     * 获取网段（前三位）
     */
    private static String getNetworkSegment(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length >= 3) {
            return parts[0] + "." + parts[1] + "." + parts[2];
        }
        return ip;
    }
}
