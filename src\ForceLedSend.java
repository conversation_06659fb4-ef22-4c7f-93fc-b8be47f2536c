import java.net.*;
import java.io.*;

/**
 * 强制LED数据发送工具
 * 即使设备无响应也尝试发送LED控制数据
 */
public class ForceLedSend {
    
    public static void main(String[] args) {
        String ledIp = "*************";
        
        System.out.println("=== 强制LED数据发送工具 ===");
        System.out.println("目标设备: " + ledIp);
        System.out.println("注意: 此工具将强制发送数据，即使设备无响应");
        System.out.println("==========================================");
        
        // 1. 尝试多个端口发送
        tryMultiplePorts(ledIp);
        
        // 2. 尝试UDP发送
        tryUdpSend(ledIp);
        
        // 3. 尝试广播发送
        tryBroadcastSend();
        
        System.out.println("\n=== 发送尝试完成 ===");
    }
    
    static void tryMultiplePorts(String ip) {
        System.out.println("\n1. 尝试TCP端口发送:");
        
        // 常见LED控制端口
        int[] ports = {5005, 5006, 5007, 5008, 8080, 8888, 10000, 10001};
        
        for (int port : ports) {
            System.out.println("   尝试端口 " + port + ":");
            
            try {
                // 创建LED控制数据包
                byte[] ledData = createLedDataPacket();
                
                // 尝试发送
                boolean sent = sendTcpData(ip, port, ledData);
                
                if (sent) {
                    System.out.println("     ✓ 数据发送成功");
                } else {
                    System.out.println("     ✗ 数据发送失败");
                }
                
            } catch (Exception e) {
                System.out.println("     ✗ 发送异常: " + e.getMessage());
            }
        }
    }
    
    static void tryUdpSend(String ip) {
        System.out.println("\n2. 尝试UDP发送:");
        
        int[] udpPorts = {5005, 5006, 8080};
        
        for (int port : udpPorts) {
            System.out.println("   UDP端口 " + port + ":");
            
            try {
                byte[] ledData = createLedDataPacket();
                boolean sent = sendUdpData(ip, port, ledData);
                
                if (sent) {
                    System.out.println("     ✓ UDP数据发送成功");
                } else {
                    System.out.println("     ✗ UDP数据发送失败");
                }
                
            } catch (Exception e) {
                System.out.println("     ✗ UDP发送异常: " + e.getMessage());
            }
        }
    }
    
    static void tryBroadcastSend() {
        System.out.println("\n3. 尝试广播发送:");
        
        try {
            String broadcastIp = "*************"; // 网段广播地址
            System.out.println("   广播地址: " + broadcastIp);
            
            byte[] ledData = createLedDataPacket();
            boolean sent = sendUdpData(broadcastIp, 5005, ledData);
            
            if (sent) {
                System.out.println("   ✓ 广播数据发送成功");
                System.out.println("   网段内的LED设备可能会收到此数据");
            } else {
                System.out.println("   ✗ 广播数据发送失败");
            }
            
        } catch (Exception e) {
            System.out.println("   ✗ 广播发送异常: " + e.getMessage());
        }
    }
    
    static byte[] createLedDataPacket() {
        // 创建一个简单的LED控制数据包
        // 这里模拟一个基本的LED显示命令
        
        String text = "Hello LED!";
        byte[] textBytes = text.getBytes();
        
        // 构建数据包: 头部 + 命令 + 数据长度 + 文本数据 + 校验
        byte[] packet = new byte[10 + textBytes.length];
        
        // 包头 (4字节)
        packet[0] = 0x55;           // 同步字节1
        packet[1] = (byte)0xAA;     // 同步字节2
        packet[2] = 0x01;           // 版本号
        packet[3] = 0x00;           // 保留字节
        
        // 命令字 (2字节)
        packet[4] = 0x10;           // 命令类型: 文本显示
        packet[5] = 0x01;           // 子命令: 单行文本
        
        // 数据长度 (2字节)
        packet[6] = (byte)(textBytes.length & 0xFF);
        packet[7] = (byte)((textBytes.length >> 8) & 0xFF);
        
        // 显示参数 (2字节)
        packet[8] = 0x06;           // 特效: 连续左移
        packet[9] = 0x0A;           // 速度: 10
        
        // 文本数据
        System.arraycopy(textBytes, 0, packet, 10, textBytes.length);
        
        return packet;
    }
    
    static boolean sendTcpData(String ip, int port, byte[] data) {
        try (Socket socket = new Socket()) {
            // 设置较短的连接超时
            socket.connect(new InetSocketAddress(ip, port), 2000);
            
            OutputStream out = socket.getOutputStream();
            out.write(data);
            out.flush();
            
            System.out.println("       发送了 " + data.length + " 字节数据");
            
            // 尝试读取响应
            InputStream in = socket.getInputStream();
            socket.setSoTimeout(1000);
            
            try {
                byte[] response = new byte[1024];
                int bytesRead = in.read(response);
                if (bytesRead > 0) {
                    System.out.println("       收到响应: " + bytesRead + " 字节");
                }
            } catch (SocketTimeoutException e) {
                System.out.println("       无响应数据");
            }
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    static boolean sendUdpData(String ip, int port, byte[] data) {
        try (DatagramSocket socket = new DatagramSocket()) {
            InetAddress address = InetAddress.getByName(ip);
            DatagramPacket packet = new DatagramPacket(data, data.length, address, port);
            
            socket.send(packet);
            System.out.println("       发送了 " + data.length + " 字节UDP数据");
            
            // 尝试接收响应
            byte[] responseBuffer = new byte[1024];
            DatagramPacket responsePacket = new DatagramPacket(responseBuffer, responseBuffer.length);
            
            socket.setSoTimeout(1000);
            try {
                socket.receive(responsePacket);
                System.out.println("       收到UDP响应: " + responsePacket.getLength() + " 字节");
            } catch (SocketTimeoutException e) {
                System.out.println("       无UDP响应");
            }
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
}
