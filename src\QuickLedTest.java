import java.net.*;
import java.io.*;
import java.util.*;

/**
 * 快速LED连通性测试
 */
public class QuickLedTest {
    
    public static void main(String[] args) {
        String ledIp = "*************";
        
        System.out.println("=== LED连通性快速测试 ===");
        System.out.println("目标LED IP: " + ledIp);
        System.out.println();
        
        // 1. 显示本机网络信息
        showLocalNetwork();
        
        // 2. 测试连通性
        testConnectivity(ledIp);
        
        // 3. 扫描端口
        scanPorts(ledIp);
        
        // 4. 给出建议
        giveAdvice(ledIp);
    }
    
    static void showLocalNetwork() {
        System.out.println("1. 本机网络信息:");
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                if (ni.isUp() && !ni.isLoopback()) {
                    Enumeration<InetAddress> addresses = ni.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress addr = addresses.nextElement();
                        if (addr instanceof Inet4Address && !addr.isLoopbackAddress()) {
                            System.out.println("   " + ni.getDisplayName() + ": " + addr.getHostAddress());
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("   获取失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    static void testConnectivity(String ip) {
        System.out.println("2. 连通性测试:");
        try {
            InetAddress addr = InetAddress.getByName(ip);
            boolean reachable = addr.isReachable(3000);
            System.out.println("   网络可达性: " + (reachable ? "✓ 成功" : "✗ 失败"));
        } catch (Exception e) {
            System.out.println("   网络可达性: ✗ 异常 - " + e.getMessage());
        }
        System.out.println();
    }
    
    static void scanPorts(String ip) {
        System.out.println("3. 端口扫描:");
        int[] ports = {5005, 5006, 8080, 8888, 10000};
        boolean found = false;
        
        for (int port : ports) {
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(ip, port), 1000);
                System.out.println("   端口 " + port + ": ✓ 开放");
                found = true;
            } catch (Exception e) {
                System.out.println("   端口 " + port + ": ✗ 关闭");
            }
        }
        
        if (!found) {
            System.out.println("   未发现开放的LED控制端口");
        }
        System.out.println();
    }
    
    static void giveAdvice(String ledIp) {
        System.out.println("4. 诊断结果和建议:");
        
        try {
            // 获取本机IP
            String localIp = getLocalIP();
            System.out.println("   本机IP: " + localIp);
            System.out.println("   LED IP: " + ledIp);
            
            // 检查网段
            String[] localParts = localIp.split("\\.");
            String[] ledParts = ledIp.split("\\.");
            
            boolean sameSegment = localParts[0].equals(ledParts[0]) && 
                                localParts[1].equals(ledParts[1]) && 
                                localParts[2].equals(ledParts[2]);
            
            if (sameSegment) {
                System.out.println("   网段状态: ✓ 同一网段");
                System.out.println("   建议: 检查LED设备是否开机，网线是否连接正常");
            } else {
                System.out.println("   网段状态: ✗ 不同网段");
                System.out.println("   解决方案:");
                System.out.println("   1. 修改LED IP为: " + localParts[0] + "." + localParts[1] + "." + localParts[2] + ".116");
                System.out.println("   2. 或修改本机IP为: " + ledParts[0] + "." + ledParts[1] + "." + ledParts[2] + ".100");
                System.out.println("   3. 或配置路由器进行网段转发");
            }
            
        } catch (Exception e) {
            System.out.println("   分析失败: " + e.getMessage());
        }
        
        System.out.println("\n   通用检查清单:");
        System.out.println("   □ LED屏是否通电");
        System.out.println("   □ 网线连接是否正常");
        System.out.println("   □ LED控制卡指示灯状态");
        System.out.println("   □ 防火墙是否阻止连接");
        System.out.println("   □ 使用厂商配置工具检查");
    }
    
    static String getLocalIP() throws Exception {
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface ni = interfaces.nextElement();
            if (ni.isUp() && !ni.isLoopback()) {
                Enumeration<InetAddress> addresses = ni.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (addr instanceof Inet4Address && !addr.isLoopbackAddress()) {
                        return addr.getHostAddress();
                    }
                }
            }
        }
        return "127.0.0.1";
    }
}
