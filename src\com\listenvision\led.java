package com.listenvision;// 注意：此命名空间不可改，否则会报错

public class led {
	 
	
	/********************************************************************************************
	 *	InitLedType				初始化控制卡型号，程序启动，初始化调用一次即可
	 *
	 *	参数说明
	 *				LedType		卡型号   0   	T/A/U/XC/W
										1   	E
								 		2		X
								 		3		C

	 ********************************************************************************************/
	public native static void InitLedType(int LedType);
	
	/********************************************************************************************
	 *	InitLedRgb			当Led上显示的文字区域的颜色与下发的不一致, 请确认Led 屏的RGB顺序,并调用此接口
	 *
	 *	参数说明
	 *				Rgb		模组的RGB顺序,仅C卡有效,其他卡固定为0. C卡时, 0:  R->G->B 1: G->R->B 2:R->B->G 
									3:B->R->G 4:B->G->R 5:G->B->R
	 ********************************************************************************************/
	public native static void InitLedRgb (int Rgb);
	
	/********************************************************************************************
	 *	CreateProgram				创建节目对象，成功返回节目对象句柄，注意此处屏宽高及颜色参数必需与设置屏参的屏宽高及颜色一致，否则发送时会提示错误
	 *
	 *	参数说明
	 *				LedWidth		屏的宽度
	 *				LedHeight		屏的高度
	 *				ColorType		屏的颜色 1.单色  2.双基色  3.三基色     注：C卡全彩参数为3      X系列卡参数固定为 4
	 *				GrayLevel		灰度等级  赋值  1-5对应的灰度等级分别为 无,4,8,16,32     除C卡外，其它卡传0
	 *				SaveType		节目保存位置，默认为0保存为flash节目，3保存为ram节目。注：flash节目掉电不清除，ram节目掉电清除。应用场景需要实时刷新的，建议保持为ram节目
	 											目前仅C卡程序才支持切换,  其他卡默认出货为flash程序,如果需要RAM程序请联系业务或者在官网下载,然后使用Led Player对卡进行升级
	 *	返回值
	 *				0				创建节目对象失败
	 *				非0				创建节目对象成功
	 ********************************************************************************************/
	public native static long CreateProgram(int LedWidth,int LedHeight,int ColorType,int GrayLevel,int SaveType);
	
	/*********************************************************************************************
	 *	AddProgram					添加一个节目
	 *	
	 *	参数说明
	 *				hProgram		节目对象句柄
	 *				ProgramNo		节目号（取值范围0-255)（从0开始）
	 *				ProgramTime		节目播放时长 0.节目播放时长  非0.指定播放时长
	 *				LoopCount		循环播放次数
	 *	返回值
	 *				0				成功
	 *				非0				失败	
	 ********************************************************************************************/
	public native static int AddProgram(long hProgram,int ProgramNo,int ProgramTime,int LoopCount);
	
	/*********************************************************************************************
	 *	LV_AddImageTextArea				添加一个图文区域
	 *	
	 *	参数说明
	 *				hProgram			节目对象句柄
	 *				ProgramNo			节目号（取值范围0-255)（从0开始）
	 *				AreaNo				区域号（取值范围1-255）
	 *				l					区域左上角横坐标
	 *				t					区域左上角纵坐标
	 *				w					区域宽度
	 *				h					区域高度
	 *				nLayout				区域层号,0.前景区（默认） 1.背景区  注：除C系列，其它默认为1
	 *	返回值
	 *				0					成功
	 *				非0					失败	
	 ********************************************************************************************/
	public native static int AddImageTextArea(long hProgram,int ProgramNo,int AreaNo,int l,int t,int w,int h,int nLayout);
	
	/*********************************************************************************************
	 * AddFileToImageTextArea				添加一个文件到图文区
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号（取值范围0-255)（从0开始）
	 *				AreaNo					区域号（取值范围1-255）
	 *				FilePath				文件路径，支持的文件类型有 txt   bmp  jpg jpeg 
	 *				InStyle					入场特技（取值范围 0-38）具体查看开发文档
	 *				nSpeed					特技速度 (取值范围1-255)值越大，速度越慢
	 *				DelayTime				停留时间 (1-65535)秒	注：当入场特技为连续左移、连续右移、连续上移、连续下移时，此参数无效		
	 *	返回值
	 *				0						成功
	 *				非0						失败	
	 ********************************************************************************************/
	public native static int AddFileToImageTextArea(long hProgram,int ProgramNo,int AreaNo,String FilePath,int InStyle,int nSpeed,int DelayTime);
	
	/*********************************************************************************************
	 * AddMultiLineTextToImageTextArea		添加一个多行文本到图文区
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号（取值范围0-255)（从0开始）
	 *				AreaNo					区域号（取值范围1-255）
	 *				AddType					添加的类型  0.为字符串  1.文件（只支持txt）
	 *				AddStr					AddType为0则为字符串数据,AddType为1则为文件路径     换行符（\n）
	 *				FontName				字库文件路径	
	 *				FontSize				字体大小
	 *				FontColor				字体颜色  格式BBGGRR   0xff 红色  0xff00 绿色  0xffff黄色  
	 *				FontBold				是否加粗 0不加粗 1加粗
	 *				FontItalic				是否是斜体  0 不斜 1斜
	 *				FontUnderline			是否下划线 0不加下划线 1加下划线
	 *				InStyle					入场特技（取值范围 0-38）具体查看开发文档
	 *				nSpeed					特技速度 (取值范围1-255)值越大，速度越慢
	 *				DelayTime				停留时间 (1-65535)秒	注：当入场特技为连续左移、连续右移、连续上移、连续下移时，此参数无效
	 *				nAlignment				左右居中对齐方式 0.左对齐  1.水平居中  2.右对齐  （注意：只对字符串和txt文件有效）
	 *				IsVCenter				是否垂直居中  0.置顶（默认） 1.垂直居中
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AddMultiLineTextToImageTextArea(long hProgram,int ProgramNo,int AreaNo,int AddType,String AddStr,String FontName,int FontSize,int FontColor,int FontBold,int FontItalic,int FontUnderline,int InStyle,int nSpeed,int DelayTime,int nAlignment,int IsVCenter);
	
	/*********************************************************************************************
	 * AddStaticTextToImageTextArea			添加一个静止文本到图文区
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号（取值范围0-255)（从0开始）
	 *				AreaNo					区域号（取值范围1-255）
	 *				AddType					添加的类型  0.为字符串  1.文件（只支持txt）
	 *				AddStr					AddType为0则为字符串数据,AddType为1则为文件路径
	 *				FontName				字库文件路径	
	 *				FontSize				字体大小
	 *				FontColor				字体颜色  格式BBGGRR 0xff 红色  0xff00 绿色  0xffff黄色
	 *				FontBold				是否加粗 0不加粗 1加粗
	 *				FontItalic				是否是斜体  0 不斜 1斜
	 *				FontUnderline			是否下划线 0不加下划线 1加下划线
	 *				DelayTime				停留时间	(1-65535)  秒    注：当入场特技为连续左移、连续右移、连续上移、连续下移时，此参数无效
	 *				nAlignment				左右居中对齐方式 0.左对齐  1.水平居中  2.右对齐  （注意：只对字符串和txt文件有效）
	 *				IsVCenter				是否垂直居中  0.置顶（默认） 1.垂直居中
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AddStaticTextToImageTextArea(long hProgram,int ProgramNo,int AreaNo,int AddType,String AddStr,String FontName,int FontSize,int FontColor,int FontBold,int FontItalic,int FontUnderline,int DelayTime,int nAlignment,int IsVCenter);
	
	/*********************************************************************************************
	 * AddSinglelineTextToImageTextArea		添加一个单行文本到图文区
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号（取值范围0-255)（从0开始）
	 *				AreaNo					区域号（取值范围1-255）
	 *				AddType					添加的类型  0.为字符串  1.文件（只支持txt）
	 *				AddStr					AddType为0则为字符串数据,AddType为1则为文件路径
	 *				FontName				字库文件路径	
	 *				FontSize				字体大小
	 *				FontColor				字体颜色  格式BBGGRR  0xff 红色  0xff00 绿色  0xffff黄色
	 *				FontBold				是否加粗 0不加粗 1加粗
	 *				FontItalic				是否是斜体  0 不斜 1斜
	 *				FontUnderline			是否下划线 0不加下划线 1加下划线
	 *				InStyle					入场特技 （取值范围 0-38）具体查看开发文档
	 *				nSpeed					特技速度  (取值范围1-255)
	 *				DelayTime				停留时间  (1-65535)秒	注：当入场特技为连续左移、连续右移、连续上移、连续下移时，此参数无效
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AddSinglelineTextToImageTextArea(long hProgram,int ProgramNo,int AreaNo,int AddType,String AddStr,String FontName,int FontSize,int FontColor,int FontBold,int FontItalic,int FontUnderline,int InStyle,int nSpeed,int DelayTime);
	
	/*********************************************************************************************
	 * AddDigitalClockArea		添加一个数字时钟区域
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号 （取值范围0-255)（从0开始）
	 *				AreaNo					区域号 （取值范围1-255）
	 *				l						区域左上角横坐标
	 *				t						区域左上角纵坐标
	 *				w						区域宽度
	 *				h						区域高度
	 *				FontName				字库文件路径	
	 *				FontSize				字体大小
	 *				FontColor				字体颜色   格式BBGGRR 0xff 红色  0xff00 绿色  0xffff黄色
	 *				FontBold				是否加粗 0不加粗 1加粗
	 *				FontItalic				是否是斜体  0 不斜 1斜
	 *				FontUnderline			是否下划线 0不加下划线 1加下划线
	 * 				IsYear					是否显示年 1为显示 0不显示 下同
	 * 				IsWeek					是否显示星期	
	 *				IsMonth					是否显示月
	 *				IsDay					是否显示日
	 *				IsHour					是否显示时
	 *				IsMinute				是否显示分
	 *				IsSecond				是否显示秒
	 *				DateFormat				日期格式 0.YYYY年MM月DD日  1.YY年MM月DD日  2.MM/DD/YYYY  3.YYYY/MM/DD  4.YYYY-MM-DD  5.YYYY.MM.DD  6.MM.DD.YYYY  7.DD.MM.YYYY
	 *				DateColor				日期字体颜色0xff 红色  0xff00 绿色  0xffff黄色
	 *				WeekFormat				星期格式 0.星期X  1.Monday  2.Mon.
	 *				WeekColor				星期字体颜色0xff 红色  0xff00 绿色  0xffff黄色
	 *				TimeFormat				时间格式 0.HH时mm分ss秒  1.HH時mm分ss秒  2.HH:mm:ss  3.上午 HH:mm:ss  4.AM HH:mm:ss  5.HH:mm:ss 上午  6.HH:mm:ss AM
	 *				TimeColor				时间字体颜色0xff 红色  0xff00 绿色  0xffff黄色
					IsMutleLineShow			0 单行  1多行
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AddDigitalClockArea(long hProgram,int ProgramNo,int AreaNo,int l,int t,int w,int h,String FontName,int FontSize,int FontColor,int FontBold,int FontItalic,int FontUnderline,int IsYear,int IsWeek,int IsMonth,int IsDay,int IsHour,int IsMinute,int IsSecond,int DateFormat,int DateColor,int WeekFormat,int WeekColor,int TimeFormat,int TimeColor,int IsMutleLineShow);

	/*********************************************************************************************
	 * AddTimeArea		添加一个计时区域
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号 （取值范围0-255)（从0开始）
	 *				AreaNo					区域号 （取值范围1-255）
	 *				l						区域左上角横坐标
	 *				t						区域左上角纵坐标
	 *				w						区域宽度
	 *				h						区域高度
	 *				FontName				字库文件路径	
	 *				FontSize				字体大小
	 *				//FontColor				字体颜色   格式BBGGRR 0xff 红色  0xff00 绿色  0xffff黄色
	 *				FontBold				是否加粗 0不加粗 1加粗
	 *				FontItalic				是否是斜体  0 不斜 1斜
	 *				FontUnderline			是否下划线 0不加下划线 1加下划线
	 *				TimeColor				时间字体颜色0xff 红色  0xff00 绿色  0xffff黄色
	 *				TxtColor				自定义文字字体颜色0xff 红色  0xff00 绿色  0xffff黄色
	 *				ShowStyle				显示格式  0.xx天xx时xx分xx秒  1.xx天xx時xx分xx秒  2.xxDayxxHourxxMinxxSec  3.XXdXXhXXmXXs  4.xx:xx:xx:xx
	 * 				IsMutleLineShow			0 单行  1多行		
	 *				TxtStr					自定义文字字符串
	 *				nYear;                  结束年
	 *				nMonth;                 结束月
	 *				nDay;                   结束日
	 *				nHour;                  结束时
	 *				nMinute;                结束分
	 *				nSecond;                结束秒
	 *				IsShowDay;              是否显示天
     *				IsShowHour;             是否显示时
     *				IsShowMinute;           是否显示分
     *				IsShowSecond;           是否显示秒
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AddTimeArea(long hProgram,int ProgramNo,int AreaNo,int l,int t,int w,int h,String FontName,int FontSize,int FontBold,int FontItalic,int FontUnderline,int TimeColor,int TxtColor,int ShowStyle,int IsMutleLineShow,String TxtStr,int nYear,int nMonth,int nDay,int nHour,int nMinute,int nSecond,int IsShowDay,int IsShowHour,int IsShowMinute,int IsShowSecond);

	/*********************************************************************************************
	 * AddVoiceArea		添加一个语音区域 (文本中可以加入文本控制标记来对语音合成发音人、音量、语速、语调等的设置,文本控制标记请查看语音协议)
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号 （取值范围0-255)（从0开始）
	 *				AreaNo					区域号 （取值范围1-255）
     *				VoiceStr;             	文本
     *				DelayTime;           	间隔时间
     *				PlayCount;           	播放次数
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AddVoiceArea(long hProgram,int ProgramNo,int AreaNo,String VoiceStr,int DelayTime,int PlayCount);


	/*********************************************************************************************
	 * AddNeiMaArea		添加一个内码区域
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 *				ProgramNo				节目号 （取值范围0-255)（从0开始）
	 *				AreaNo					区域号 （取值范围1-255）
	 *				l						区域左上角横坐标
	 *				t						区域左上角纵坐标
	 *				w						区域宽度
	 *				h						区域高度
	 *				NeiMaStr				字符串	
	 *				FontSize				字体大小  16  24  32
	 *				FontColor				字体颜色   格式BBGGRR 0xff 红色  0xff00 绿色  0xffff黄色
	 *				InStyle					入场特技 （取值范围 0-9）
	 *				nSpeed					特技速度  (取值范围1-255)
	 *				DelayTime				停留时间  (1-65535)
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AddNeiMaArea(long hProgram,int ProgramNo,int AreaNo,int l,int t,int w,int h,String NeiMaStr,int FontSize,int FontColor,int InStyle,int nSpeed,int DelayTime);

	/*********************************************************************************************
	 *	DeleteProgram						销毁节目对象(注意：如果此节目对象不再使用，请调用此函数销毁，否则会造成内存泄露)
	 *	
	 *	参数说明
	 *				hProgram				节目对象句柄
	 ********************************************************************************************/
	public native static void DeleteProgram(long hProgram);
	
	/*********************************************************************************************
	 *	NetWorkSend							发送节目，此发送为一对一发送
	 *	
	 *	参数说明
	 *				IpStr					LED屏IP   /      广域网开发时,指定LED屏网络唯一ID
	 *				hProgram				节目对象句柄
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int NetWorkSend(String IpStr,long hProgram);
	
	
	/*********************************************************************************************
	 *	SerialPortSend							串口发送节目
	 *	
	 *	参数说明
	 *				commport					串口号  
	 *				baud					波特率 9600 115200 576000 
	 *				ledNumber					Led屏号,1~255
	 *				hProgram				节目对象句柄
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int SerialPortSend(int commport,int baud,int ledNumber, int hProgram);
	
	
	/*********************************************************************************************
	*	LV_RefreshNeiMaArea								刷新内码区域
	*	
	*	参数说明
	*				IpStr		LED屏IP 格式例如 *************   /  广域网开发时,指定LED屏网络唯一ID
	*				NeiMaStr				刷新的数据字符串,格式可以查看<<内码区域局部更新协议>>文档
	*	返回值
	 *				0						成功
	 *				非0						失败
	********************************************************************************************/
	public native static int RefreshNeiMaArea(String IpStr, String NeiNaStr);
	
	/*********************************************************************************************
	 *	SetBasicInfo						设置基本屏参
	 *	
	 *	参数说明
	 *				IpStr					LED屏的IP
	 *				ColorType				屏的颜色 1.单色  2.双基色  3.三基色
	 *				GrayLevel				灰度等级， 赋值  1-5对应的灰度等级分别为 无,4,8,16,32
	 *				LedWidth				屏的宽度点数
	 *				LedHeight				屏的高度点数
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int SetBasicInfo(String IpStr,int ColorType,int GrayLevel,int LedWidth,int LedHeight);
	
	/*********************************************************************************************
	 *	SetOEDA								设置OE DA
	 *	
	 *	参数说明
	 *				IpStr					LED屏的IP
	 *				Oe						OE  0.低有效  1.高有效
	 *				Da						DA  0.负极性  1.正极性
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int SetOEDA(String IpStr,int Oe,int Da);
	
	/*********************************************************************************************
	 *	AdjustTime							校时
	 *	
	 *	参数说明
	 *				IpStr					LED屏的IP
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int AdjustTime(String IpStr);
	
	/*********************************************************************************************
	 *	PowerOnOff							开关屏
	 *	
	 *	参数说明
	 *				IpStr					LED屏的IP
         *				OnOff					开关值  0.开屏  1.关屏   2.重启
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int PowerOnOff(String IpStr,int OnOff);
	
	/*********************************************************************************************
	 *	TimePowerOnOff						定时开关屏
	 *	
	 *	参数说明 注：当参数起始时间和结束时间都都为0时，既是取消定时开关屏
	 *				IpStr					LED屏的IP
	 *				StartHour				起始小时
	 *				StartMinute				起始分钟
	 *				EndHour					结束小时
	 *				EndMinute				结束分钟
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int TimePowerOnOff(String IpStr,int StartHour,int StartMinute,int EndHour,int EndMinute);
	
	/*********************************************************************************************
	 *	SetBrightness						设置亮度
	 *	
	 *	参数说明
	 *				IpStr					LED屏的IP
	 *				BrightnessValue			亮度值 0~15
	 *	返回值
	 *				0						成功
	 *				非0						失败	
	 ********************************************************************************************/
	public native static int SetBrightness(String IpStr,int BrightnessValue);
	
	/*********************************************************************************************
	 *	LedTest								LED测试
	 *	
	 *	参数说明
	 *				IpStr					LED屏的IP
	 *				TestValue				测试值
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int LedTest(String IpStr,int TestValue);

	/*********************************************************************************************
	 *	TestOnline								LED测试通讯是否正常
	 *	
	 *	参数说明
	 *				IpStr					LED屏的IP
	 *	返回值
	 *				0						成功
	 *				非0						失败
	 ********************************************************************************************/
	public native static int TestOnline(String IpStr);
	
	/*********************************************************************************************
	 *	GetErrorCodeInfo								获取错误信息
	 *	
	 *	参数说明
	 *				nErrCode					错误码
	 *	返回值
	 *				返回错误信息
	 ********************************************************************************************/
	public native static String GetErrorCodeInfo(int nErrCode);

	/*********************************************************************************************
	*	LedInitServer			启动控制卡心跳包服务。注：C2M C4M才支持
	*	
	*	参数说明
	*				port			监听的端口
	*	返回值
	*				0						成功
	*				非0						失败，调用LV_GetError来获取错误信息	
	********************************************************************************************/
	public native static int LedInitServer(int port);
	
	/*********************************************************************************************
	*	LedShudownServer			断开控制卡心跳包服务。注：C2M C4M才支持
	********************************************************************************************/
	public native static int LedShudownServer();
	
	/*********************************************************************************************
	*	RegisterLedServerCallback			注册回调，动态库才会调用LedServerCallback，实时告知上下线控制卡。注：C2M C4M才支持
	*	cb									回调对象                         
	********************************************************************************************/
	public native static void RegisterLedServerCallback(ledCallBack cb);
	

	static 
	{
		//System.loadLibrary("ledplayer7");
		System.load("/home/<USER>/ledpaly7/demo_java/src/lib/libledplayer7.so");
	}

	//注：
	/*  特技值对应
	0=立即显示
	1=随机
	2=左移
	3=右移
	4=上移
	5=下移
	6=连续左移
	7=连续右移
	8=连续上移
	9=连续下移
	10=闪烁
	11=激光字(向上)
	12=激光字(向下)
	13=激光字(向左)
	14=激光字(向右)
	15=水平交叉拉幕
	16=上下交叉拉幕
	17=左右切入
	18=上下切入
	19=左覆盖
	20=右覆盖
	21=上覆盖
	22=下覆盖
	23=水平百叶(左右)
	24=水平百叶(右左)
	25=垂直百叶(上下)
	26=垂直百叶(下上)
	27=左右对开
	28=上下对开
	29=左右闭合
	30=上下闭合
	31=向左拉伸
	32=向右拉伸
	33=向上拉伸
	34=向下拉伸
	35=分散向左拉伸
	36=分散向右拉伸
	37=冒泡
	38=下雪
	*/
}
