import com.listenvision.led;

/**
 * 安全的LED控制测试类 - 修复了内存泄漏问题
 * 使用资源管理器确保内存正确释放
 */
public class SafeLedTest {
    
    public static void main(String[] args) {
        String strIp = "*************";
        int ledWidth = 64;
        int ledHeight = 64;
        int colorType = 3;
        int grayLevel = 0;
        
        System.out.println("=== 安全LED控制测试 ===");
        System.out.println("目标IP: " + strIp);
        
        // 初始化LED类型
        led.InitLedType(0);
        
        // 测试各种LED功能（安全版本）
        testSingleLineTextSafe(strIp, ledWidth, ledHeight, colorType, grayLevel);
        testMultiLineTextSafe(strIp, ledWidth, ledHeight, colorType, grayLevel);
        testPictureSafe(strIp, ledWidth, ledHeight, colorType, grayLevel);
        testTwoMutiTextAndBackgroundSafe(strIp, ledWidth, ledHeight, colorType, grayLevel);
        
        System.out.println("=== 所有测试完成，无内存泄漏 ===");
    }
    
    /**
     * 安全的单行文本测试
     */
    public static void testSingleLineTextSafe(String strIp, int ledWidth, int ledHeight, int colorType, int grayLevel) {
        System.out.println("\n测试单行文本（安全版本）...");
        
        try (LedProgramManager program = new LedProgramManager(ledWidth, ledHeight, colorType, grayLevel, 0)) {
            
            int result = program.addProgram(0, 0, 1);
            if (result != 0) {
                System.out.println("添加程序失败: " + led.GetErrorCodeInfo(result));
                return;
            }
            
            result = program.addImageTextArea(0, 1, 0, 0, ledWidth, ledHeight, 1);
            if (result != 0) {
                System.out.println("添加图文区域失败: " + led.GetErrorCodeInfo(result));
                return;
            }
            
            result = program.addSinglelineTextToImageTextArea(0, 1, 0, 
                "Safe LED Display Test!", "./font/simsun.ttc", 20, 0xff, 0, 0, 0, 6, 10, 0);
            if (result != 0) {
                System.out.println("添加单行文本失败: " + led.GetErrorCodeInfo(result));
                return;
            }
            
            result = program.sendToDevice(strIp);
            if (result != 0) {
                System.out.println("发送失败: " + led.GetErrorCodeInfo(result));
            } else {
                System.out.println("✓ 单行文本发送成功");
            }
            
        } // 自动释放资源
    }
    
    /**
     * 安全的多行文本测试
     */
    public static void testMultiLineTextSafe(String strIp, int ledWidth, int ledHeight, int colorType, int grayLevel) {
        System.out.println("\n测试多行文本（安全版本）...");
        
        try (LedProgramManager program = new LedProgramManager(ledWidth, ledHeight, colorType, grayLevel, 0)) {
            
            program.addProgram(0, 0, 1);
            program.addImageTextArea(0, 1, 0, 0, ledWidth, ledHeight, 1);
            
            int result = program.addMultiLineTextToImageTextArea(0, 1, 0, 
                "多行文本\n测试内容", "./font/simsun.ttc", 12, 0xff, 0, 0, 0, 0, 4, 10, 0, 0);
            
            if (result != 0) {
                System.out.println("添加多行文本失败: " + led.GetErrorCodeInfo(result));
                return;
            }
            
            result = program.sendToDevice(strIp);
            if (result == 0) {
                System.out.println("✓ 多行文本发送成功");
            } else {
                System.out.println("发送失败: " + led.GetErrorCodeInfo(result));
            }
            
        } // 自动释放资源
    }
    
    /**
     * 安全的图片测试
     */
    public static void testPictureSafe(String strIp, int ledWidth, int ledHeight, int colorType, int grayLevel) {
        System.out.println("\n测试图片显示（安全版本）...");
        
        try (LedProgramManager program = new LedProgramManager(ledWidth, ledHeight, colorType, grayLevel, 0)) {
            
            program.addProgram(0, 0, 1);
            program.addImageTextArea(0, 1, 0, 0, ledWidth, ledHeight, 1);
            
            int result = program.addFileToImageTextArea(0, 1, "test.bmp", 1, 4, 2);
            if (result != 0) {
                System.out.println("添加图片失败: " + led.GetErrorCodeInfo(result));
                return;
            }
            
            result = program.sendToDevice(strIp);
            if (result == 0) {
                System.out.println("✓ 图片发送成功");
            } else {
                System.out.println("发送失败: " + led.GetErrorCodeInfo(result));
            }
            
        } // 自动释放资源
    }
    
    /**
     * 安全的两个多行文本和背景测试（修复了原始的内存泄漏问题）
     */
    public static void testTwoMutiTextAndBackgroundSafe(String strIp, int ledWidth, int ledHeight, int colorType, int grayLevel) {
        System.out.println("\n测试两个多行文本和背景（安全版本）...");
        
        try (LedProgramManager program = new LedProgramManager(ledWidth, ledHeight, colorType, grayLevel, 0)) {
            
            int nProgramId = 0;
            int nAreaId = 1;
            
            // 添加程序
            int errCode = program.addProgram(nProgramId, 0, 1);
            if (errCode != 0) {
                System.out.println("添加程序失败: " + led.GetErrorCodeInfo(errCode));
                return; // 安全退出，资源会自动释放
            }
            
            // 背景图片区域
            errCode = program.addImageTextArea(nProgramId, nAreaId, 0, 0, ledWidth, ledHeight, 0);
            if (errCode != 0) {
                System.out.println("添加背景区域失败: " + led.GetErrorCodeInfo(errCode));
                return; // 安全退出
            }
            
            errCode = program.addFileToImageTextArea(nProgramId, nAreaId, "back.bmp", 0, 4, 65535);
            if (errCode != 0) {
                System.out.println("添加背景图片失败: " + led.GetErrorCodeInfo(errCode));
                return; // 🔧 修复：现在安全退出，不会泄漏内存
            }
            
            // 第一个文本区域
            nAreaId++;
            errCode = program.addImageTextArea(nProgramId, nAreaId, 0, 1, ledWidth-2, ledHeight/2-2, 1);
            if (errCode != 0) {
                System.out.println("添加第一个文本区域失败: " + led.GetErrorCodeInfo(errCode));
                return; // 安全退出
            }
            
            errCode = program.addMultiLineTextToImageTextArea(nProgramId, nAreaId, 0, "安全文本001", 
                "./font/simsun.ttc", 12, 0xff, 1, 0, 0, 0, 4, 10, 0, 0);
            if (errCode != 0) {
                System.out.println("添加第一个文本失败: " + led.GetErrorCodeInfo(errCode));
                return; // 🔧 修复：现在安全退出，不会泄漏内存
            }
            
            // 第二个文本区域
            nAreaId++;
            errCode = program.addImageTextArea(nProgramId, nAreaId, 0, ledHeight/2+1, ledWidth, ledHeight/2-2, 1);
            if (errCode != 0) {
                System.out.println("添加第二个文本区域失败: " + led.GetErrorCodeInfo(errCode));
                return; // 安全退出
            }
            
            errCode = program.addMultiLineTextToImageTextArea(nProgramId, nAreaId, 0, "安全文本002", 
                "./font/simsun.ttc", 12, 0xff, 1, 0, 0, 0, 4, 10, 0, 0);
            if (errCode != 0) {
                System.out.println("添加第二个文本失败: " + led.GetErrorCodeInfo(errCode));
                return; // 🔧 修复：现在安全退出，不会泄漏内存
            }
            
            // 发送到设备
            errCode = program.sendToDevice(strIp);
            if (errCode != 0) {
                System.out.println("发送失败: " + led.GetErrorCodeInfo(errCode));
            } else {
                System.out.println("✓ 两个多行文本和背景发送成功");
            }
            
        } // 🔧 修复：无论如何退出，资源都会被自动释放
        
        System.out.println("✓ 方法执行完成，资源已安全释放");
    }
}
